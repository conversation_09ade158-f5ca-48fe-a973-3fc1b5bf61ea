Starting master-s_2.0.py at 15-09-2025 13:01:21.60 
Running master-s_2.0.py... 

Processing:   0%|          | 0/1 [00:00<?, ?it/s]
Processing: 100%|##########| 1/1 [00:17<00:00, 17.54s/it]
Processing: 100%|##########| 1/1 [00:17<00:00, 17.54s/it]

Starting:   0%|          | 0/1 [00:00<?, ?it/s]
Starting: 100%|##########| 1/1 [00:00<00:00, 47.09it/s]

Processing:   0%|          | 0/1 [00:00<?, ?it/s]
Processing: 100%|##########| 1/1 [00:08<00:00,  8.79s/it]
Processing: 100%|##########| 1/1 [00:08<00:00,  8.79s/it]

Processing:   0%|          | 0/1 [00:00<?, ?it/s]
Processing: 100%|##########| 1/1 [00:31<00:00, 31.62s/it]
Processing: 100%|##########| 1/1 [00:31<00:00, 31.62s/it]

Processing:   0%|          | 0/1 [00:00<?, ?it/s]
Processing: 100%|##########| 1/1 [00:00<00:00,  3.41it/s]
Processing: 100%|##########| 1/1 [00:00<00:00,  3.41it/s]

Processing:   0%|          | 0/1 [00:00<?, ?it/s]C:\Users\<USER>\OneDrive\My Files\master-s_2.0.py:269: DtypeWarning: Columns (20,23,32,36,37,38,39) have mixed types. Specify dtype option on import or set low_memory=False.
  df_concat = pd.concat([pd.read_csv(f) for f in csv_files ], ignore_index=True)

Processing: 100%|##########| 1/1 [00:02<00:00,  2.80s/it]
Processing: 100%|##########| 1/1 [00:02<00:00,  2.80s/it]

Processing:   0%|          | 0/1 [00:00<?, ?it/s]
Processing: 100%|##########| 1/1 [00:00<00:00,  9.71it/s]
Processing: 100%|##########| 1/1 [00:00<00:00,  9.71it/s]

Processing:   0%|          | 0/1 [00:00<?, ?it/s]
Processing: 100%|##########| 1/1 [00:39<00:00, 39.87s/it]
Processing: 100%|##########| 1/1 [00:39<00:00, 39.87s/it]

Finishing:   0%|          | 0/1 [00:00<?, ?it/s]
Finishing: 100%|##########| 1/1 [00:35<00:00, 35.34s/it]
Finishing: 100%|##########| 1/1 [00:35<00:00, 35.34s/it]
SUCCESS: master-s_2.0.py completed successfully at 15-09-2025 13:03:39.26 
Running direct_unsubs.py... 
SUCCESS: direct_unsubs.py completed successfully at 15-09-2025 13:04:00.78 
