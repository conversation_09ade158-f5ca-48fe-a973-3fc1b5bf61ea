Starting master-s_2.0.py at 29-09-2025 12:14:07.35 
Running master-s_2.0.py... 

Processing:   0%|          | 0/1 [00:00<?, ?it/s]
Processing: 100%|##########| 1/1 [00:16<00:00, 16.10s/it]
Processing: 100%|##########| 1/1 [00:16<00:00, 16.10s/it]

Starting:   0%|          | 0/1 [00:00<?, ?it/s]
Starting: 100%|##########| 1/1 [00:02<00:00,  2.49s/it]
Starting: 100%|##########| 1/1 [00:02<00:00,  2.49s/it]

Processing:   0%|          | 0/1 [00:00<?, ?it/s]
Processing: 100%|##########| 1/1 [00:06<00:00,  6.08s/it]
Processing: 100%|##########| 1/1 [00:06<00:00,  6.08s/it]

Processing:   0%|          | 0/1 [00:00<?, ?it/s]
Processing: 100%|##########| 1/1 [00:24<00:00, 24.35s/it]
Processing: 100%|##########| 1/1 [00:24<00:00, 24.35s/it]

Processing:   0%|          | 0/1 [00:00<?, ?it/s]
Processing: 100%|##########| 1/1 [00:00<00:00,  6.23it/s]
Processing: 100%|##########| 1/1 [00:00<00:00,  6.23it/s]

Processing:   0%|          | 0/1 [00:00<?, ?it/s]C:\Users\<USER>\OneDrive\My Files\master-s_2.0.py:269: DtypeWarning: Columns (20,23,32,36,37,38,39) have mixed types. Specify dtype option on import or set low_memory=False.
  df_concat = pd.concat([pd.read_csv(f) for f in csv_files ], ignore_index=True)

Processing: 100%|##########| 1/1 [00:03<00:00,  3.40s/it]
Processing: 100%|##########| 1/1 [00:03<00:00,  3.40s/it]

Processing:   0%|          | 0/1 [00:00<?, ?it/s]
Processing: 100%|##########| 1/1 [00:00<00:00, 10.91it/s]

Processing:   0%|          | 0/1 [00:00<?, ?it/s]
Processing: 100%|##########| 1/1 [00:40<00:00, 40.15s/it]
Processing: 100%|##########| 1/1 [00:40<00:00, 40.15s/it]

Finishing:   0%|          | 0/1 [00:00<?, ?it/s]
Finishing: 100%|##########| 1/1 [00:46<00:00, 46.31s/it]
Finishing: 100%|##########| 1/1 [00:46<00:00, 46.31s/it]
SUCCESS: master-s_2.0.py completed successfully at 29-09-2025 12:16:28.11 
Running direct_unsubs.py... 
SUCCESS: direct_unsubs.py completed successfully at 29-09-2025 12:16:48.24 
