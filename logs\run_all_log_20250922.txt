Starting master-s_2.0.py at 22-09-2025 14:53:21.19 
Running master-s_2.0.py... 

Processing:   0%|          | 0/1 [00:00<?, ?it/s]
Processing: 100%|##########| 1/1 [00:16<00:00, 16.25s/it]
Processing: 100%|##########| 1/1 [00:16<00:00, 16.25s/it]

Starting:   0%|          | 0/1 [00:00<?, ?it/s]
Starting: 100%|##########| 1/1 [00:01<00:00,  1.59s/it]
Starting: 100%|##########| 1/1 [00:01<00:00,  1.59s/it]

Processing:   0%|          | 0/1 [00:00<?, ?it/s]
Processing: 100%|##########| 1/1 [00:07<00:00,  7.31s/it]
Processing: 100%|##########| 1/1 [00:07<00:00,  7.31s/it]

Processing:   0%|          | 0/1 [00:00<?, ?it/s]
Processing: 100%|##########| 1/1 [00:29<00:00, 29.94s/it]
Processing: 100%|##########| 1/1 [00:29<00:00, 29.94s/it]

Processing:   0%|          | 0/1 [00:00<?, ?it/s]
Processing: 100%|##########| 1/1 [00:00<00:00,  7.20it/s]
Processing: 100%|##########| 1/1 [00:00<00:00,  7.20it/s]

Processing:   0%|          | 0/1 [00:00<?, ?it/s]C:\Users\<USER>\OneDrive\My Files\master-s_2.0.py:269: DtypeWarning: Columns (20,24,33,36,37,38,39) have mixed types. Specify dtype option on import or set low_memory=False.
  df_concat = pd.concat([pd.read_csv(f) for f in csv_files ], ignore_index=True)

Processing: 100%|##########| 1/1 [00:02<00:00,  2.91s/it]
Processing: 100%|##########| 1/1 [00:02<00:00,  2.91s/it]

Processing:   0%|          | 0/1 [00:00<?, ?it/s]
Processing: 100%|##########| 1/1 [00:00<00:00,  8.84it/s]
Processing: 100%|##########| 1/1 [00:00<00:00,  8.84it/s]

Processing:   0%|          | 0/1 [00:00<?, ?it/s]
Processing: 100%|##########| 1/1 [00:42<00:00, 42.93s/it]
Processing: 100%|##########| 1/1 [00:42<00:00, 42.93s/it]

Finishing:   0%|          | 0/1 [00:00<?, ?it/s]
Finishing: 100%|##########| 1/1 [00:49<00:00, 49.96s/it]
Finishing: 100%|##########| 1/1 [00:49<00:00, 49.96s/it]
SUCCESS: master-s_2.0.py completed successfully at 22-09-2025 14:55:54.11 
Running direct_unsubs.py... 
SUCCESS: direct_unsubs.py completed successfully at 22-09-2025 14:56:15.81 
