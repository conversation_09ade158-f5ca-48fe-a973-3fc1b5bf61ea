##HIC

import os
import glob
import pandas as pd
import re
import warnings
from datetime import datetime

# Import rich_progress for gradient progress bars
import rich_progress

from pandas.core.common import SettingWithCopyWarning
warnings.simplefilter(action="ignore", category=SettingWithCopyWarning)

# Helper functions for rich progress bars
def print_header(title):
    """Print a colorful header with the given title."""
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    rich_progress.print_status(f"\n{title}", "header")
    rich_progress.print_status(timestamp, "info")
    rich_progress.print_status("=" * 50, "info")

def print_section(title):
    """Print a section header."""
    rich_progress.print_status(f"\n>> {title}", "info")
    rich_progress.print_status("-" * (len(title) + 4), "info")

# Print welcome header
print_header("HIC Email Domain Filtering Tool")

# Get the path from the user
print_section("Input Path")
path = input("Loc: ")
os.chdir(path)
rich_progress.print_status(f"Working directory: {os.getcwd()}", "info")

# Extract conference segment name
print_section("Conference Segment Detection")

# Define a regex pattern to match the desired segment, including spaces
pattern = r"\\([\w\s-]+\d{4})\\?"

# Search for the pattern in the path
match = re.search(pattern, path)

if match:
    csn = match.group(1)
    rich_progress.print_status(f"Found segment: {csn}", "success")
else:
    rich_progress.print_status(f"Desired segment not found in path: {path}", "warning")
    # Prompt for manual input
    csn = input("Please enter the conference segment name (e.g., 'Conference 2023'): ")
    if csn.strip():
        rich_progress.print_status(f"Using manually entered segment: {csn}", "info")
    else:
        rich_progress.print_status("No segment name provided. Exiting.", "error")
        exit()

# Find all CSV files in the directory
print_section("Finding CSV Files")
csv_files = glob.glob('*.csv')
if not csv_files:
    rich_progress.print_status("No CSV files found in the directory.", "error")
    exit()
rich_progress.print_status(f"Found {len(csv_files)} CSV files", "success")

# Read each CSV file
dfs = []
for csv_file in csv_files:
    # Add low_memory=False here
    df = pd.read_csv(csv_file, low_memory=False)
    dfs.append(df)

# Concatenate all dataframes
rich_progress.print_status("Concatenating files...", "info")
d2_EVENT = pd.concat(dfs, ignore_index=True)
rich_progress.print_status(f"Successfully read {len(d2_EVENT)} records from {len(csv_files)} files", "success")

df_hic = d2_EVENT[d2_EVENT["Email"].str.endswith(".edu") |
                d2_EVENT["Email"].str.endswith(".mil") |
                d2_EVENT["Email"].str.endswith(".gov") |
                d2_EVENT["Email"].str.endswith(".pl") |
                d2_EVENT["Email"].str.endswith(".it") |
                d2_EVENT["Email"].str.endswith(".au") |
                d2_EVENT["Email"].str.endswith(".at") |
                d2_EVENT["Email"].str.endswith(".be") |
                d2_EVENT["Email"].str.endswith(".ca") |
                d2_EVENT["Email"].str.endswith(".dk") |
                d2_EVENT["Email"].str.endswith(".uk") |
                d2_EVENT["Email"].str.endswith(".eu") |
                d2_EVENT["Email"].str.endswith(".fi") |
                d2_EVENT["Email"].str.endswith(".fr") |
                d2_EVENT["Email"].str.endswith(".ge") |
                d2_EVENT["Email"].str.endswith(".gr") |
                d2_EVENT["Email"].str.endswith(".nz") |
                d2_EVENT["Email"].str.endswith(".nl") |
                d2_EVENT["Email"].str.endswith(".no") |
                d2_EVENT["Email"].str.endswith(".se") |
                d2_EVENT["Email"].str.endswith(".ch") |
                d2_EVENT["Email"].str.endswith(".ae")]


df_hiccn = d2_EVENT[d2_EVENT["Email"].str.endswith("163.com") |
                d2_EVENT["Email"].str.endswith("qq.com") |
                d2_EVENT["Email"].str.endswith(".126.com") |
                d2_EVENT["Email"].str.endswith("sina.com") |
                d2_EVENT["Email"].str.endswith("sohu.com") |
                d2_EVENT["Email"].str.endswith("tom.com") |
                d2_EVENT["Email"].str.endswith("aliyun.com") |
                d2_EVENT["Email"].str.endswith("21cn.com") |
                d2_EVENT["Email"].str.endswith("baidu.com") |
                d2_EVENT["Email"].str.endswith(".cn") |
                d2_EVENT["Email"].str.endswith("yeah.net") |
                d2_EVENT["Email"].str.endswith("sogou.com") |
                d2_EVENT["Email"].str.endswith("163.net") |
                d2_EVENT["Email"].str.endswith("sina.net") |
                d2_EVENT["Email"].str.endswith("chinaren.com")]

df_hichk = d2_EVENT[d2_EVENT["Email"].str.endswith(".hk") |
                d2_EVENT["Email"].str.endswith("hkt.com") |
                d2_EVENT["Email"].str.endswith("netvigator.com")]

df_hictw = d2_EVENT[d2_EVENT["Email"].str.endswith(".tw", na=False)]

df_hicsg = d2_EVENT[d2_EVENT["Email"].str.endswith(".sg", na=False)]

df_hicmy = d2_EVENT[d2_EVENT["Email"].str.endswith(".my", na=False)]

df_hic1 = d2_EVENT[d2_EVENT["Email"].str.endswith(".jp") |
                d2_EVENT["Email"].str.endswith(".cn") |
                d2_EVENT["Email"].str.endswith(".tw") |
                d2_EVENT["Email"].str.endswith(".kr") |
                d2_EVENT["Email"].str.endswith(".hk") |
                d2_EVENT["Email"].str.endswith(".us") |
                d2_EVENT["Email"].str.endswith(".ca") |
                d2_EVENT["Email"].str.endswith(".uk") |
                d2_EVENT["Email"].str.endswith(".de") |
                d2_EVENT["Email"].str.endswith(".fr") |
                d2_EVENT["Email"].str.endswith(".it") |
                d2_EVENT["Email"].str.endswith(".es") |
                d2_EVENT["Email"].str.endswith(".nl") |
                d2_EVENT["Email"].str.endswith(".se") |
                d2_EVENT["Email"].str.endswith(".no") |
                d2_EVENT["Email"].str.endswith(".dk") |
                d2_EVENT["Email"].str.endswith(".fi") |
                d2_EVENT["Email"].str.endswith(".pl") |
                d2_EVENT["Email"].str.endswith(".ro")]


df_hic2 = d2_EVENT[d2_EVENT["Email"].str.endswith(".au") |
                d2_EVENT["Email"].str.endswith(".nz") |
                d2_EVENT["Email"].str.endswith(".ch") |
                d2_EVENT["Email"].str.endswith(".pt") |
                d2_EVENT["Email"].str.endswith(".gr") |
                d2_EVENT["Email"].str.endswith(".at") |
                d2_EVENT["Email"].str.endswith(".be") |
                d2_EVENT["Email"].str.endswith(".lu") |
                d2_EVENT["Email"].str.endswith(".ie") |
                d2_EVENT["Email"].str.endswith(".cz") |
                d2_EVENT["Email"].str.endswith(".ee") |
                d2_EVENT["Email"].str.endswith(".hu") |
                d2_EVENT["Email"].str.endswith(".cy") |
                d2_EVENT["Email"].str.endswith(".hr") |
                d2_EVENT["Email"].str.endswith(".sg") |
                d2_EVENT["Email"].str.endswith(".ge") |
                d2_EVENT["Email"].str.endswith(".al") |
                d2_EVENT["Email"].str.endswith(".br") |
                d2_EVENT["Email"].str.endswith(".mo") |
		        d2_EVENT["Email"].str.endswith(".tn") |
                d2_EVENT["Email"].str.endswith(".uz")]

df_hic3 = d2_EVENT[d2_EVENT["Email"].str.endswith(".ae") |
                d2_EVENT["Email"].str.endswith(".qa") |
                d2_EVENT["Email"].str.endswith(".kw") |
                d2_EVENT["Email"].str.endswith(".sa") |
                d2_EVENT["Email"].str.endswith(".om") |
                d2_EVENT["Email"].str.endswith(".cl") |
                d2_EVENT["Email"].str.endswith(".mx") |
                d2_EVENT["Email"].str.endswith(".jo") |
                d2_EVENT["Email"].str.endswith(".in") |
                d2_EVENT["Email"].str.endswith(".my") |
                d2_EVENT["Email"].str.endswith(".th") |
                d2_EVENT["Email"].str.endswith(".ph") |
                d2_EVENT["Email"].str.endswith(".vn") |
                d2_EVENT["Email"].str.endswith(".mk") |
                d2_EVENT["Email"].str.endswith(".za") |
                d2_EVENT["Email"].str.endswith(".zm") |
                d2_EVENT["Email"].str.endswith(".ci") |
                d2_EVENT["Email"].str.endswith(".et")]

df_hic_eu = d2_EVENT[d2_EVENT["Email"].str.endswith(".al") |
                   d2_EVENT["Email"].str.endswith(".ad") |
                   d2_EVENT["Email"].str.endswith(".am") |
                   d2_EVENT["Email"].str.endswith(".at") |
                   d2_EVENT["Email"].str.endswith(".az") |
                   d2_EVENT["Email"].str.endswith(".by") |
                   d2_EVENT["Email"].str.endswith(".be") |
                   d2_EVENT["Email"].str.endswith(".ba") |
                   d2_EVENT["Email"].str.endswith(".bg") |
                   d2_EVENT["Email"].str.endswith(".hr") |
                   d2_EVENT["Email"].str.endswith(".cy") |
                   d2_EVENT["Email"].str.endswith(".cz") |
                   d2_EVENT["Email"].str.endswith(".dk") |
                   d2_EVENT["Email"].str.endswith(".ee") |
                   d2_EVENT["Email"].str.endswith(".fi") |
                   d2_EVENT["Email"].str.endswith(".fr") |
                   d2_EVENT["Email"].str.endswith(".ge") |
                   d2_EVENT["Email"].str.endswith(".de") |
                   d2_EVENT["Email"].str.endswith(".gr") |
                   d2_EVENT["Email"].str.endswith(".hu") |
                   d2_EVENT["Email"].str.endswith(".is") |
                   d2_EVENT["Email"].str.endswith(".ie") |
                   d2_EVENT["Email"].str.endswith(".it") |
                   d2_EVENT["Email"].str.endswith(".kz") |
                   d2_EVENT["Email"].str.endswith(".xk") |
                   d2_EVENT["Email"].str.endswith(".lv") |
                   d2_EVENT["Email"].str.endswith(".li") |
                   d2_EVENT["Email"].str.endswith(".lt") |
                   d2_EVENT["Email"].str.endswith(".lu") |
                   d2_EVENT["Email"].str.endswith(".mt") |
                   d2_EVENT["Email"].str.endswith(".md") |
                   d2_EVENT["Email"].str.endswith(".mc") |
                   d2_EVENT["Email"].str.endswith(".me") |
                   d2_EVENT["Email"].str.endswith(".nl") |
                   d2_EVENT["Email"].str.endswith(".mk") |
                   d2_EVENT["Email"].str.endswith(".no") |
                   d2_EVENT["Email"].str.endswith(".pl") |
                   d2_EVENT["Email"].str.endswith(".pt") |
                   d2_EVENT["Email"].str.endswith(".ro") |
                   d2_EVENT["Email"].str.endswith(".ru") |
                   d2_EVENT["Email"].str.endswith(".sm") |
                   d2_EVENT["Email"].str.endswith(".rs") |
                   d2_EVENT["Email"].str.endswith(".sk") |
                   d2_EVENT["Email"].str.endswith(".si") |
                   d2_EVENT["Email"].str.endswith(".es") |
                   d2_EVENT["Email"].str.endswith(".se") |
                   d2_EVENT["Email"].str.endswith(".ch") |
                   d2_EVENT["Email"].str.endswith(".tr") |
                   d2_EVENT["Email"].str.endswith(".ua") |
                   d2_EVENT["Email"].str.endswith(".uk") |
                   d2_EVENT["Email"].str.endswith(".va")]


df_hic_na = d2_EVENT[d2_EVENT["Email"].str.endswith(".ca") |
                    d2_EVENT["Email"].str.endswith(".us") |
                    d2_EVENT["Email"].str.endswith(".mx") |
                    d2_EVENT["Email"].str.endswith(".bz") |
                    d2_EVENT["Email"].str.endswith(".cr") |
                    d2_EVENT["Email"].str.endswith(".sv") |
                    d2_EVENT["Email"].str.endswith(".gt") |
                    d2_EVENT["Email"].str.endswith(".hn") |
                    d2_EVENT["Email"].str.endswith(".ni") |
                    d2_EVENT["Email"].str.endswith(".pa") |
                    d2_EVENT["Email"].str.endswith(".ag") |
                    d2_EVENT["Email"].str.endswith(".bs") |
                    d2_EVENT["Email"].str.endswith(".bb") |
                    d2_EVENT["Email"].str.endswith(".cu") |
                    d2_EVENT["Email"].str.endswith(".dm") |
                    d2_EVENT["Email"].str.endswith(".do") |
                    d2_EVENT["Email"].str.endswith(".gd") |
                    d2_EVENT["Email"].str.endswith(".ht") |
                    d2_EVENT["Email"].str.endswith(".jm") |
                    d2_EVENT["Email"].str.endswith(".kn") |
                    d2_EVENT["Email"].str.endswith(".lc") |
                    d2_EVENT["Email"].str.endswith(".vc") |
                    d2_EVENT["Email"].str.endswith(".tt")]

df_hic_sa = d2_EVENT[d2_EVENT["Email"].str.endswith(".ar") |
                    d2_EVENT["Email"].str.endswith(".bo") |
                    d2_EVENT["Email"].str.endswith(".br") |
                    d2_EVENT["Email"].str.endswith(".cl") |
                    d2_EVENT["Email"].str.endswith(".co") |
                    d2_EVENT["Email"].str.endswith(".ec") |
                    d2_EVENT["Email"].str.endswith(".gy") |
                    d2_EVENT["Email"].str.endswith(".py") |
                    d2_EVENT["Email"].str.endswith(".pe") |
                    d2_EVENT["Email"].str.endswith(".sr") |
                    d2_EVENT["Email"].str.endswith(".uy") |
                    d2_EVENT["Email"].str.endswith(".ve")]


df_hic_asia = d2_EVENT[d2_EVENT["Email"].str.endswith(".af") |
                       d2_EVENT["Email"].str.endswith(".am") |
                       d2_EVENT["Email"].str.endswith(".az") |
                       d2_EVENT["Email"].str.endswith(".bh") |
                       d2_EVENT["Email"].str.endswith(".bd") |
                       d2_EVENT["Email"].str.endswith(".bt") |
                       d2_EVENT["Email"].str.endswith(".bn") |
                       d2_EVENT["Email"].str.endswith(".kh") |
                       d2_EVENT["Email"].str.endswith(".cn") |
                       d2_EVENT["Email"].str.endswith(".cy") |
                       d2_EVENT["Email"].str.endswith(".ge") |
                       d2_EVENT["Email"].str.endswith(".in") |
                       d2_EVENT["Email"].str.endswith(".id") |
                       d2_EVENT["Email"].str.endswith(".ir") |
                       d2_EVENT["Email"].str.endswith(".iq") |
                       d2_EVENT["Email"].str.endswith(".il") |
                       d2_EVENT["Email"].str.endswith(".jp") |
                       d2_EVENT["Email"].str.endswith(".jo") |
                       d2_EVENT["Email"].str.endswith(".kz") |
                       d2_EVENT["Email"].str.endswith(".kw") |
                       d2_EVENT["Email"].str.endswith(".kg") |
                       d2_EVENT["Email"].str.endswith(".la") |
                       d2_EVENT["Email"].str.endswith(".lb") |
                       d2_EVENT["Email"].str.endswith(".my") |
                       d2_EVENT["Email"].str.endswith(".mv") |
                       d2_EVENT["Email"].str.endswith(".mn") |
                       d2_EVENT["Email"].str.endswith(".mm") |
                       d2_EVENT["Email"].str.endswith(".np") |
                       d2_EVENT["Email"].str.endswith(".kp") |
                       d2_EVENT["Email"].str.endswith(".om") |
                       d2_EVENT["Email"].str.endswith(".pk") |
                       d2_EVENT["Email"].str.endswith(".ps") |
                       d2_EVENT["Email"].str.endswith(".ph") |
                       d2_EVENT["Email"].str.endswith(".qa") |
                       d2_EVENT["Email"].str.endswith(".sa") |
                       d2_EVENT["Email"].str.endswith(".sg") |
                       d2_EVENT["Email"].str.endswith(".kr") |
                       d2_EVENT["Email"].str.endswith(".lk") |
                       d2_EVENT["Email"].str.endswith(".sy") |
                       d2_EVENT["Email"].str.endswith(".tw") |
                       d2_EVENT["Email"].str.endswith(".tj") |
                       d2_EVENT["Email"].str.endswith(".th") |
                       d2_EVENT["Email"].str.endswith(".tl") |
                       d2_EVENT["Email"].str.endswith(".tr") |
                       d2_EVENT["Email"].str.endswith(".tm") |
                       d2_EVENT["Email"].str.endswith(".ae") |
                       d2_EVENT["Email"].str.endswith(".uz") |
                       d2_EVENT["Email"].str.endswith(".vn") |
                       d2_EVENT["Email"].str.endswith(".ye")]

df_hic_africa = d2_EVENT[d2_EVENT["Email"].str.endswith(".af") |
                       d2_EVENT["Email"].str.endswith(".am") |
                       d2_EVENT["Email"].str.endswith(".az") |
                       d2_EVENT["Email"].str.endswith(".bh") |
                       d2_EVENT["Email"].str.endswith(".bd") |
                       d2_EVENT["Email"].str.endswith(".bt") |
                       d2_EVENT["Email"].str.endswith(".bn") |
                       d2_EVENT["Email"].str.endswith(".kh") |
                       d2_EVENT["Email"].str.endswith(".cn") |
                       d2_EVENT["Email"].str.endswith(".cy") |
                       d2_EVENT["Email"].str.endswith(".ge") |
                       d2_EVENT["Email"].str.endswith(".in") |
                       d2_EVENT["Email"].str.endswith(".id") |
                       d2_EVENT["Email"].str.endswith(".ir") |
                       d2_EVENT["Email"].str.endswith(".iq") |
                       d2_EVENT["Email"].str.endswith(".il") |
                       d2_EVENT["Email"].str.endswith(".jp") |
                       d2_EVENT["Email"].str.endswith(".jo") |
                       d2_EVENT["Email"].str.endswith(".kz") |
                       d2_EVENT["Email"].str.endswith(".kw") |
                       d2_EVENT["Email"].str.endswith(".kg") |
                       d2_EVENT["Email"].str.endswith(".la") |
                       d2_EVENT["Email"].str.endswith(".lb") |
                       d2_EVENT["Email"].str.endswith(".my") |
                       d2_EVENT["Email"].str.endswith(".mv") |
                       d2_EVENT["Email"].str.endswith(".mn") |
                       d2_EVENT["Email"].str.endswith(".mm") |
                       d2_EVENT["Email"].str.endswith(".np") |
                       d2_EVENT["Email"].str.endswith(".kp") |
                       d2_EVENT["Email"].str.endswith(".om") |
                       d2_EVENT["Email"].str.endswith(".pk") |
                       d2_EVENT["Email"].str.endswith(".ps") |
                       d2_EVENT["Email"].str.endswith(".ph") |
                       d2_EVENT["Email"].str.endswith(".qa") |
                       d2_EVENT["Email"].str.endswith(".sa") |
                       d2_EVENT["Email"].str.endswith(".sg") |
                       d2_EVENT["Email"].str.endswith(".kr") |
                       d2_EVENT["Email"].str.endswith(".lk") |
                       d2_EVENT["Email"].str.endswith(".sy") |
                       d2_EVENT["Email"].str.endswith(".tw") |
                       d2_EVENT["Email"].str.endswith(".tj") |
                       d2_EVENT["Email"].str.endswith(".th") |
                       d2_EVENT["Email"].str.endswith(".tl") |
                       d2_EVENT["Email"].str.endswith(".tr") |
                       d2_EVENT["Email"].str.endswith(".tm") |
                       d2_EVENT["Email"].str.endswith(".ae") |
                       d2_EVENT["Email"].str.endswith(".uz") |
                       d2_EVENT["Email"].str.endswith(".vn") |
                       d2_EVENT["Email"].str.endswith(".ye")]


df_hic_oceania = d2_EVENT[d2_EVENT["Email"].str.endswith(".au") |
                          d2_EVENT["Email"].str.endswith(".nz") |
                          d2_EVENT["Email"].str.endswith(".fj") |
                          d2_EVENT["Email"].str.endswith(".pg") |
                          d2_EVENT["Email"].str.endswith(".sb") |
                          d2_EVENT["Email"].str.endswith(".vu") |
                          d2_EVENT["Email"].str.endswith(".fm") |
                          d2_EVENT["Email"].str.endswith(".nr") |
                          d2_EVENT["Email"].str.endswith(".pw") |
                          d2_EVENT["Email"].str.endswith(".ws") |
                          d2_EVENT["Email"].str.endswith(".to") |
                          d2_EVENT["Email"].str.endswith(".tv") |
                          d2_EVENT["Email"].str.endswith(".ki") |
                          d2_EVENT["Email"].str.endswith(".mh")]

df_hic_antarctica = d2_EVENT[d2_EVENT["Email"].str.endswith(".aq", na=False)]

df_hic_arctic = d2_EVENT[d2_EVENT["Email"].str.endswith(".ca") |  # Canada
                         d2_EVENT["Email"].str.endswith(".gl") |  # Greenland (Denmark)
                         d2_EVENT["Email"].str.endswith(".ru") |  # Russia
                         d2_EVENT["Email"].str.endswith(".no") |  # Norway
                         d2_EVENT["Email"].str.endswith(".se") |  # Sweden
                         d2_EVENT["Email"].str.endswith(".fi") |  # Finland
                         d2_EVENT["Email"].str.endswith(".is") |  # Iceland
                         d2_EVENT["Email"].str.endswith(".us")]    # United States (Alaska)

df_hicedu = d2_EVENT[d2_EVENT["Email"].str.contains(".edu.", na=False)]

df_hicjp = d2_EVENT[d2_EVENT["Email"].str.endswith(".jp", na=False)]

df_hickr = d2_EVENT[d2_EVENT["Email"].str.endswith(".kr") |
                d2_EVENT["Email"].str.endswith("naver.com") |
                d2_EVENT["Email"].str.endswith("daum.com") |
                d2_EVENT["Email"].str.endswith("hanmail.com")]

df_hicnz = d2_EVENT[d2_EVENT["Email"].str.endswith(".nz", na=False)]

df_hicau = d2_EVENT[d2_EVENT["Email"].str.endswith(".au", na=False)]

# Add these dataframes for neighboring countries with visa-free access to Singapore

# Malaysia (already in your code as df_hicmy)
# df_hicmy = d2_EVENT[d2_EVENT["Email"].str.endswith(".my", na=False)]

# Indonesia
df_hicid = d2_EVENT[d2_EVENT["Email"].str.endswith(".id", na=False)]

# Thailand
df_hicth = d2_EVENT[d2_EVENT["Email"].str.endswith(".th", na=False)]

# Philippines
df_hicph = d2_EVENT[d2_EVENT["Email"].str.endswith(".ph", na=False)]

# Brunei
df_hicbn = d2_EVENT[d2_EVENT["Email"].str.endswith(".bn", na=False)]

# Vietnam
df_hicvn = d2_EVENT[d2_EVENT["Email"].str.endswith(".vn", na=False)]

# Laos
df_hicla = d2_EVENT[d2_EVENT["Email"].str.endswith(".la", na=False)]

# Cambodia
df_hickh = d2_EVENT[d2_EVENT["Email"].str.endswith(".kh", na=False)]

# Myanmar
df_hicmm = d2_EVENT[d2_EVENT["Email"].str.endswith(".mm", na=False)]

# Create a combined dataframe for all ASEAN countries
df_hic_asean = pd.concat([df_hicsg, df_hicmy, df_hicid, df_hicth, df_hicph, df_hicbn, df_hicvn, df_hicla, df_hickh, df_hicmm], ignore_index=True)

# Print section for filtering results
print_section("Filtering Results")

# Create a table-like output for better readability
rich_progress.print_status("Domain filtering complete. Results:", "success")
rich_progress.print_status("-" * 40, "info")
rich_progress.print_status(f"{'Category':<20} {'Count':>10}", "info")
rich_progress.print_status("-" * 40, "info")

# Print counts with consistent formatting
rich_progress.print_status(f"{'High Priority':<20} {len(df_hic):>10}", "info")
rich_progress.print_status(f"{'1st Priority':<20} {len(df_hic1):>10}", "info")
rich_progress.print_status(f"{'2nd Priority':<20} {len(df_hic2):>10}", "info")
rich_progress.print_status(f"{'3rd Priority':<20} {len(df_hic3):>10}", "info")
rich_progress.print_status(f"{'China':<20} {len(df_hiccn):>10}", "info")
rich_progress.print_status(f"{'Hong Kong':<20} {len(df_hichk):>10}", "info")
rich_progress.print_status(f"{'Taiwan':<20} {len(df_hictw):>10}", "info")
rich_progress.print_status(f"{'Singapore':<20} {len(df_hicsg):>10}", "info")
rich_progress.print_status(f"{'Malaysia':<20} {len(df_hicmy):>10}", "info")
rich_progress.print_status(f"{'Education':<20} {len(df_hicedu):>10}", "info")
rich_progress.print_status(f"{'Japan':<20} {len(df_hicjp):>10}", "info")
rich_progress.print_status(f"{'Korea':<20} {len(df_hickr):>10}", "info")
rich_progress.print_status(f"{'New Zealand':<20} {len(df_hicnz):>10}", "info")
rich_progress.print_status(f"{'Australia':<20} {len(df_hicau):>10}", "info")
rich_progress.print_status(f"{'Asia':<20} {len(df_hic_asia):>10}", "info")
rich_progress.print_status(f"{'Africa':<20} {len(df_hic_africa):>10}", "info")
rich_progress.print_status(f"{'North America':<20} {len(df_hic_na):>10}", "info")
rich_progress.print_status(f"{'South America':<20} {len(df_hic_sa):>10}", "info")
rich_progress.print_status(f"{'Europe':<20} {len(df_hic_eu):>10}", "info")
rich_progress.print_status(f"{'Antarctica':<20} {len(df_hic_antarctica):>10}", "info")
rich_progress.print_status(f"{'Oceania':<20} {len(df_hic_oceania):>10}", "info")
rich_progress.print_status(f"{'Arctic':<20} {len(df_hic_arctic):>10}", "info")
rich_progress.print_status("-" * 40, "info")

# Create output directories
print_section("Creating Output Directories")
priority_dir = os.path.join(os.getcwd(), "priority")
continent_dir = os.path.join(os.getcwd(), "continent")
countries_dir = os.path.join(os.getcwd(), "countries")

# Create all directories
os.makedirs(priority_dir, exist_ok=True)
os.makedirs(continent_dir, exist_ok=True)
os.makedirs(countries_dir, exist_ok=True)

rich_progress.print_status(f"Created priority directory: {priority_dir}", "info")
rich_progress.print_status(f"Created continent directory: {continent_dir}", "info")
rich_progress.print_status(f"Created countries directory: {countries_dir}", "info")

print_section("Organizing Output Files")

# Define all dataframes to save with their filenames and target directories
# Format: (dataframe, filename, description, directory)
files_to_save = [
    # Priority files
    (df_hic, f"{csn}_High.csv", "High Priority", priority_dir),
    #(df_hic1, f"{csn}_1st_priority.csv", "1st Priority", priority_dir),
    #(df_hic2, f"{csn}_2nd_priority.csv", "2nd Priority", priority_dir),
    #(df_hic3, f"{csn}_3rd_priority.csv", "3rd Priority", priority_dir),
    #(df_hicedu, f"{csn}_Education.csv", "Education", priority_dir),

    # Country files
    (df_hiccn, f"{csn}_China.csv", "China", countries_dir),
    (df_hichk, f"{csn}_Hongkong.csv", "Hong Kong", countries_dir),
    (df_hictw, f"{csn}_Taiwan.csv", "Taiwan", countries_dir),
    (df_hicsg, f"{csn}_Singapore.csv", "Singapore", countries_dir),
    (df_hicmy, f"{csn}_Malaysia.csv", "Malaysia", countries_dir),
    (df_hicjp, f"{csn}_Japan.csv", "Japan", countries_dir),
    (df_hickr, f"{csn}_Korea.csv", "Korea", countries_dir),
    (df_hicnz, f"{csn}_NewZealand.csv", "New Zealand", countries_dir),
    (df_hicau, f"{csn}_Australia.csv", "Australia", countries_dir),

    # Continent files
    (df_hic_asia, f"{csn}_Asia.csv", "Asia", continent_dir),
    (df_hic_africa, f"{csn}_Africa.csv", "Africa", continent_dir),
    (df_hic_na, f"{csn}_North America.csv", "North America", continent_dir),
    (df_hic_sa, f"{csn}_South America.csv", "South America", continent_dir),
    (df_hic_eu, f"{csn}_Europe.csv", "Europe", continent_dir),
    (df_hic_oceania, f"{csn}_Oceania.csv", "Oceania", continent_dir),
    (df_hic_antarctica, f"{csn}_Antarctica.csv", "Antarctica", continent_dir),
    (df_hic_arctic, f"{csn}_Arctic.csv", "Arctic", continent_dir)
]

# Create a progress bar for saving files
save_bar, update_save = rich_progress.create_progress_bar(
    total=len(files_to_save),
    description="Saving files",
    color_scheme="green"
)

# Save each file with progress tracking
for df, filename, category, directory in files_to_save:
    try:
        output_path = os.path.join(directory, filename)
        df.to_csv(output_path, encoding='utf-8-sig', index=False)
        update_save(1, f"Saved {category} ({len(df)} records) to {os.path.basename(directory)}")
    except Exception as e:
        rich_progress.print_status(f"Error saving {filename}: {str(e)}", "error")
        update_save(1, f"Error with {filename}")

# Stop the progress bar
save_bar.stop()


# Create East Asia combined file
print_section("Creating East Asia Combined File")
rich_progress.print_status("Combining East Asia records...", "info")
df_cust = pd.concat([df_hiccn, df_hichk, df_hictw, df_hicsg, df_hicjp, df_hickr, df_hicmy, df_hicnz, df_hicau])
rich_progress.print_status(f"East Asia combined: {len(df_cust)} records", "success")

# Save East Asia combined file to continent directory
east_asia_path = os.path.join(continent_dir, f"{csn}_East_Asia.csv")
df_cust.to_csv(east_asia_path, encoding='utf-8-sig', index=False)
rich_progress.print_status(f"Saved East Asia combined file to {os.path.basename(continent_dir)}/{os.path.basename(east_asia_path)}", "success")

# Print completion message
print_header("Processing Completed Successfully!")
rich_progress.print_status(f"Conference segment: {csn}", "success")
rich_progress.print_status(f"Total records processed: {len(d2_EVENT)}", "success")

# Count files by directory
priority_files = sum(1 for _, _, _, dir in files_to_save if dir == priority_dir)
countries_files = sum(1 for _, _, _, dir in files_to_save if dir == countries_dir)
continent_files = sum(1 for _, _, _, dir in files_to_save if dir == continent_dir) + 1  # +1 for East Asia

rich_progress.print_status(f"Total output files created: {len(files_to_save) + 1}", "success")
rich_progress.print_status(f"Files in priority folder: {priority_files}", "info")
#rich_progress.print_status(f"Files in countries folder: {countries_files}", "info")
#rich_progress.print_status(f"Files in continent folder: {continent_files}", "info")
rich_progress.print_status(f"Output directories: {priority_dir}, {countries_dir}, {continent_dir}", "info")

# Create an 'asean' folder if it doesn't exist
asean_folder = os.path.join(os.getcwd(), "asean")
if not os.path.exists(asean_folder):
    os.makedirs(asean_folder)
    rich_progress.print_status(f"Created directory: {asean_folder}", "info")
else:
    rich_progress.print_status(f"Directory already exists: {asean_folder}", "info")

# Save individual country files in the asean folder
#df_hicid.to_csv(os.path.join(asean_folder, f"{csn}_Indonesia.csv"), index=False)
#df_hicth.to_csv(os.path.join(asean_folder, f"{csn}_Thailand.csv"), index=False)
#df_hicph.to_csv(os.path.join(asean_folder, f"{csn}_Philippines.csv"), index=False)
#df_hicbn.to_csv(os.path.join(asean_folder, f"{csn}_Brunei.csv"), index=False)
#df_hicvn.to_csv(os.path.join(asean_folder, f"{csn}_Vietnam.csv"), index=False)
#df_hicla.to_csv(os.path.join(asean_folder, f"{csn}_Laos.csv"), index=False)
#df_hickh.to_csv(os.path.join(asean_folder, f"{csn}_Cambodia.csv"), index=False)
#df_hicmm.to_csv(os.path.join(asean_folder, f"{csn}_Myanmar.csv"), index=False)

# Save combined ASEAN file in the asean folder
#df_hic_asean.to_csv(os.path.join(asean_folder, f"{csn}_ASEAN_Countries.csv"), index=False)

#rich_progress.print_status(f"Saved filtered data for ASEAN countries in {asean_folder}", "success")
