Starting master-s_2.0.py at 06-10-2025 16:45:47.31 
Running master-s_2.0.py... 

Processing:   0%|          | 0/1 [00:00<?, ?it/s]
Processing: 100%|##########| 1/1 [00:22<00:00, 22.80s/it]
Processing: 100%|##########| 1/1 [00:22<00:00, 22.80s/it]

Starting:   0%|          | 0/1 [00:00<?, ?it/s]
Starting: 100%|##########| 1/1 [00:01<00:00,  1.98s/it]
Starting: 100%|##########| 1/1 [00:01<00:00,  1.98s/it]

Processing:   0%|          | 0/1 [00:00<?, ?it/s]
Processing: 100%|##########| 1/1 [00:12<00:00, 12.08s/it]
Processing: 100%|##########| 1/1 [00:12<00:00, 12.08s/it]

Processing:   0%|          | 0/1 [00:00<?, ?it/s]
Processing: 100%|##########| 1/1 [00:40<00:00, 40.61s/it]
Processing: 100%|##########| 1/1 [00:40<00:00, 40.61s/it]

Processing:   0%|          | 0/1 [00:00<?, ?it/s]
Processing: 100%|##########| 1/1 [00:00<00:00,  4.66it/s]
Processing: 100%|##########| 1/1 [00:00<00:00,  4.66it/s]

Processing:   0%|          | 0/1 [00:00<?, ?it/s]C:\Users\<USER>\OneDrive\My Files\master-s_2.0.py:269: DtypeWarning: Columns (20,23,32,36,37,38,39) have mixed types. Specify dtype option on import or set low_memory=False.
  df_concat = pd.concat([pd.read_csv(f) for f in csv_files ], ignore_index=True)
C:\Users\<USER>\OneDrive\My Files\master-s_2.0.py:269: DtypeWarning: Columns (20,24,33,36,37,38,39) have mixed types. Specify dtype option on import or set low_memory=False.
  df_concat = pd.concat([pd.read_csv(f) for f in csv_files ], ignore_index=True)

Processing: 100%|##########| 1/1 [00:04<00:00,  4.17s/it]
Processing: 100%|##########| 1/1 [00:04<00:00,  4.17s/it]

Processing:   0%|          | 0/1 [00:00<?, ?it/s]
Processing: 100%|##########| 1/1 [00:00<00:00, 11.23it/s]

Processing:   0%|          | 0/1 [00:00<?, ?it/s]
Processing: 100%|##########| 1/1 [00:40<00:00, 40.84s/it]
Processing: 100%|##########| 1/1 [00:40<00:00, 40.84s/it]

Finishing:   0%|          | 0/1 [00:00<?, ?it/s]
Finishing: 100%|##########| 1/1 [00:48<00:00, 48.84s/it]
Finishing: 100%|##########| 1/1 [00:48<00:00, 48.84s/it]
SUCCESS: master-s_2.0.py completed successfully at 06-10-2025 16:48:40.50 
Running direct_unsubs.py... 
SUCCESS: direct_unsubs.py completed successfully at 06-10-2025 16:49:02.06 
