#!/usr/bin/env python3
"""
Mathews Mailwizz Unsubscribers Processor

Professional script to process directories in "H:\<PERSON> Bounces and Unsubs\Mathews Postpanel Unsubs\mailwizz unsubs"
and compile email data from CSV files in each folder, saving output files with folder name + "_mwz_unsubs" 
in a "compiled unsubs" directory.

Based on email_col.py pattern but enhanced for directory processing.

Author: AI Assistant
Date: 2025-08-22
"""

import os
import glob
import pandas as pd
import numpy as np
import warnings
import shutil
from datetime import datetime
from pathlib import Path

# Import rich_progress for gradient progress bars
import rich_progress

# Set pandas Excel formatting (remove bold headers)
import pandas.io.formats.excel
pandas.io.formats.excel.ExcelFormatter.header_style = None

from pandas.core.common import SettingWithCopyWarning
warnings.simplefilter(action="ignore", category=SettingWithCopyWarning)

# Configuration
BASE_PATH = r"H:\Master Bounces and Unsubs\Mathews Postpanel Unsubs\mailwizz unsubs"
OUTPUT_FOLDER_NAME = "compiled unsubs"

def print_header(title):
    """Print a colorful header with the given title."""
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    rich_progress.print_status(f"\n{title}", "header")
    rich_progress.print_status(timestamp, "info")
    rich_progress.print_status("=" * 60, "info")

def print_section(title):
    """Print a section header."""
    rich_progress.print_status(f"\n>> {title}", "info")
    rich_progress.print_status("-" * (len(title) + 4), "info")

def get_subdirectories(base_path):
    """
    Get all subdirectories in the base path.
    
    Args:
        base_path (str): The base directory path
        
    Returns:
        list: List of subdirectory paths
    """
    subdirs = []
    try:
        for item in os.listdir(base_path):
            item_path = os.path.join(base_path, item)
            if os.path.isdir(item_path):
                subdirs.append(item_path)
        return sorted(subdirs)
    except FileNotFoundError:
        rich_progress.print_status(f"Base path not found: {base_path}", "error")
        return []
    except Exception as e:
        rich_progress.print_status(f"Error accessing base path: {e}", "error")
        return []

def process_csv_files_in_directory(directory_path):
    """
    Process all CSV files in a directory and extract emails.
    
    Args:
        directory_path (str): Path to the directory containing CSV files
        
    Returns:
        pandas.DataFrame: DataFrame containing all unique emails
    """
    # Change to the directory
    original_cwd = os.getcwd()
    
    try:
        os.chdir(directory_path)
        csv_files = glob.glob('*.csv')
        
        if not csv_files:
            rich_progress.print_status(f"No CSV files found in {os.path.basename(directory_path)}", "warning")
            return pd.DataFrame(columns=['Email'])
        
        all_emails = pd.DataFrame(columns=['Email'])
        processed_files = 0
        skipped_files = 0
        
        for f in csv_files:
            try:
                df = pd.read_csv(f, on_bad_lines='skip', low_memory=False)
                
                # Check for 'Email' or 'email' header (case-insensitive)
                email_column = None
                for col in df.columns:
                    if col.lower() == 'email':
                        email_column = col
                        break
                
                if email_column is None:
                    rich_progress.print_status(f"Warning: File {f} does not contain 'Email' column. Skipping.", "warning")
                    skipped_files += 1
                    continue

                # Extract emails
                emails = df[email_column]
                
                # Create a temporary DataFrame to hold the emails
                temp_df = pd.DataFrame({'Email': emails})
                
                # Concatenate to the main DataFrame
                all_emails = pd.concat([all_emails, temp_df], ignore_index=True)
                processed_files += 1

            except pd.errors.EmptyDataError:
                rich_progress.print_status(f"Warning: File {f} is empty. Skipping.", "warning")
                skipped_files += 1
            except pd.errors.ParserError:
                rich_progress.print_status(f"Warning: File {f} has parsing errors. Skipping.", "warning")
                skipped_files += 1
            except Exception as e:
                rich_progress.print_status(f"Error processing file {f}: {e}", "error")
                skipped_files += 1

        # Remove duplicates and NaN values
        initial_count = len(all_emails)
        all_emails.drop_duplicates(subset='Email', inplace=True)
        all_emails.dropna(subset='Email', inplace=True)
        final_count = len(all_emails)
        
        duplicates_removed = initial_count - final_count
        
        rich_progress.print_status(f"Processed {processed_files} files, skipped {skipped_files} files", "info")
        rich_progress.print_status(f"Extracted {final_count} unique emails (removed {duplicates_removed} duplicates)", "success")
        
        return all_emails
        
    finally:
        # Restore original working directory
        os.chdir(original_cwd)

def clear_and_create_output_directory(base_path):
    """
    Clear existing output directory and create a fresh one for compiled unsubs.

    Args:
        base_path (str): Base path where to create the output directory

    Returns:
        str: Path to the created output directory
    """
    output_dir = os.path.join(base_path, OUTPUT_FOLDER_NAME)

    # Clear existing directory if it exists
    if os.path.exists(output_dir):
        try:
            # Count existing files before deletion
            existing_files = glob.glob(os.path.join(output_dir, "*"))
            file_count = len(existing_files)

            if file_count > 0:
                rich_progress.print_status(f"Clearing {file_count} existing files from output directory...", "warning")
                shutil.rmtree(output_dir)
                rich_progress.print_status(f"Successfully cleared {file_count} files", "success")
            else:
                rich_progress.print_status("Output directory exists but is empty", "info")
        except Exception as e:
            rich_progress.print_status(f"Warning: Could not clear output directory: {e}", "warning")
            rich_progress.print_status("Continuing with existing files...", "info")

    # Create the directory (fresh or ensure it exists)
    os.makedirs(output_dir, exist_ok=True)
    return output_dir

def save_crm_compatible_excel(email_series, file_path):
    """
    Save emails to Excel file with CRM-compatible formatting.

    This function creates Excel files without explicitly using openpyxl engine,
    letting pandas handle the Excel creation automatically.

    Args:
        email_series (pd.Series): Series containing email addresses
        file_path (str): Path where to save the Excel file
    """
    try:
        # Create a DataFrame for the emails
        df = pd.DataFrame({'Email': email_series})

        # Clean the data - ensure all emails are strings and remove empty values
        df['Email'] = df['Email'].astype(str)
        df = df[df['Email'].str.strip() != '']
        df = df[df['Email'] != 'nan']

        # Save to Excel without specifying engine - let pandas choose
        df.to_excel(file_path, sheet_name='Sheet1', index=False, header=True)

        rich_progress.print_status(f"Excel file saved with default formatting", "info")

    except Exception as e:
        rich_progress.print_status(f"Error saving Excel file: {e}", "error")
        # Try alternative approach
        try:
            # Create a simple DataFrame and save
            email_df = pd.DataFrame({'Email': email_series.dropna()})
            email_df.to_excel(file_path, index=False)
            rich_progress.print_status(f"Excel file saved with basic formatting", "warning")
        except Exception as e2:
            rich_progress.print_status(f"Failed to save Excel file: {e2}", "error")

def create_ultra_compatible_excel(email_series, file_path):
    """
    Create an ultra-compatible Excel file using pandas default Excel writer.
    This method creates the most CRM-compatible Excel files without engine dependencies.

    Args:
        email_series (pd.Series): Series containing email addresses
        file_path (str): Path where to save the Excel file
    """
    try:
        import tempfile
        import os

        # Create a temporary CSV file for maximum compatibility
        temp_csv = tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False, encoding='utf-8-sig')

        # Write CSV data with proper formatting
        temp_csv.write('Email\n')
        for email in email_series:
            if pd.notna(email) and str(email).strip():
                # Clean and format email properly
                clean_email = str(email).strip().replace('"', '""')
                temp_csv.write(f'"{clean_email}"\n')
        temp_csv.close()

        # Read the CSV back as DataFrame
        df = pd.read_csv(temp_csv.name, encoding='utf-8-sig')

        # Ensure all data is properly formatted as strings
        df['Email'] = df['Email'].astype(str)

        # Save as Excel using pandas default (no engine specified)
        df.to_excel(file_path, sheet_name='Sheet1', index=False, header=True)

        # Clean up temporary file
        os.unlink(temp_csv.name)

        rich_progress.print_status(f"Ultra-compatible Excel file created", "info")

    except Exception as e:
        rich_progress.print_status(f"Error creating ultra-compatible Excel: {e}", "error")
        # Fallback to the regular method
        save_crm_compatible_excel(email_series, file_path)

def main():
    """Main function to process all directories."""

    # Print welcome header
    print_header("Mathews Mailwizz Unsubscribers Processor")

    # Pre-clear the compiled unsubs directory (extra safety measure)
    print_section("Pre-Clearing Output Directory")
    output_dir_path = os.path.join(BASE_PATH, OUTPUT_FOLDER_NAME)
    if os.path.exists(output_dir_path):
        try:
            existing_files = glob.glob(os.path.join(output_dir_path, "*"))
            if existing_files:
                rich_progress.print_status(f"Pre-clearing {len(existing_files)} files from compiled unsubs directory...", "warning")
                shutil.rmtree(output_dir_path)
                rich_progress.print_status("Pre-clearing completed successfully", "success")
            else:
                rich_progress.print_status("Compiled unsubs directory is already empty", "info")
        except Exception as e:
            rich_progress.print_status(f"Warning during pre-clearing: {e}", "warning")
    else:
        rich_progress.print_status("Compiled unsubs directory does not exist yet", "info")
    
    # Validate base path
    print_section("Validating Base Path")
    if not os.path.exists(BASE_PATH):
        rich_progress.print_status(f"Base path does not exist: {BASE_PATH}", "error")
        rich_progress.print_status("Please check the path and try again.", "error")
        return
    
    rich_progress.print_status(f"Base path validated: {BASE_PATH}", "success")
    
    # Get all subdirectories
    print_section("Discovering Subdirectories")
    subdirectories = get_subdirectories(BASE_PATH)
    
    if not subdirectories:
        rich_progress.print_status("No subdirectories found to process.", "warning")
        return
    
    rich_progress.print_status(f"Found {len(subdirectories)} subdirectories to process:", "success")
    for i, subdir in enumerate(subdirectories, 1):
        rich_progress.print_status(f"  {i}. {os.path.basename(subdir)}", "info")
    
    # Clear and create output directory
    print_section("Preparing Output Directory")
    output_dir = clear_and_create_output_directory(BASE_PATH)
    rich_progress.print_status(f"Output directory ready: {output_dir}", "success")
    
    # Process each subdirectory
    print_section("Processing Subdirectories")
    
    # Create progress bar for directories
    dir_bar, update_dir = rich_progress.create_progress_bar(
        total=len(subdirectories),
        description="Processing directories",
        color_scheme="green"
    )
    
    processed_count = 0
    error_count = 0
    total_emails = 0
    
    for subdir in subdirectories:
        folder_name = os.path.basename(subdir)
        update_dir(0, f"Processing {folder_name}")
        
        try:
            # Process CSV files in this directory
            emails_df = process_csv_files_in_directory(subdir)
            
            if len(emails_df) > 0:
                # Create output filenames for both formats
                csv_filename = f"{folder_name}_mwz_unsubs.csv"
                xlsx_filename = f"{folder_name}_mwz_unsubs.xlsx"
                csv_path = os.path.join(output_dir, csv_filename)
                xlsx_path = os.path.join(output_dir, xlsx_filename)

                try:
                    # Save the emails to CSV file
                    emails_df['Email'].to_csv(csv_path, index=False, encoding='utf-8-sig')

                    # Save the emails to XLSX file with ultra-compatible formatting for CRM
                    create_ultra_compatible_excel(emails_df['Email'], xlsx_path)

                    rich_progress.print_status(f"✓ Saved {len(emails_df)} emails to {csv_filename} and {xlsx_filename} (CRM-optimized)", "success")
                    processed_count += 1
                    total_emails += len(emails_df)

                except Exception as save_error:
                    rich_progress.print_status(f"✗ Error saving files for {folder_name}: {save_error}", "error")
                    error_count += 1
            else:
                rich_progress.print_status(f"✗ No emails found in {folder_name}", "warning")
            
            update_dir(1, f"Completed {folder_name}")
            
        except Exception as e:
            rich_progress.print_status(f"✗ Error processing {folder_name}: {e}", "error")
            error_count += 1
            update_dir(1, f"Error with {folder_name}")
    
    # Stop progress bar
    dir_bar.stop()
    
    # Print completion summary
    print_header("Processing Completed!")
    rich_progress.print_status(f"Directories processed successfully: {processed_count}", "success")
    rich_progress.print_status(f"Directories with errors: {error_count}", "error" if error_count > 0 else "info")
    rich_progress.print_status(f"Total unique emails compiled: {total_emails:,}", "success")
    rich_progress.print_status(f"Output directory: {output_dir}", "info")
    
    # List generated files
    if processed_count > 0:
        print_section("Generated Files")
        csv_files = glob.glob(os.path.join(output_dir, "*_mwz_unsubs.csv"))
        xlsx_files = glob.glob(os.path.join(output_dir, "*_mwz_unsubs.xlsx"))

        rich_progress.print_status(f"CSV Files ({len(csv_files)}):", "info")
        for i, file_path in enumerate(sorted(csv_files), 1):
            filename = os.path.basename(file_path)
            try:
                # Count rows in the file
                df_count = pd.read_csv(file_path)
                count = len(df_count)
                rich_progress.print_status(f"  {i}. {filename} ({count:,} emails)", "info")
            except:
                rich_progress.print_status(f"  {i}. {filename}", "info")

        rich_progress.print_status(f"\nXLSX Files ({len(xlsx_files)}):", "info")
        for i, file_path in enumerate(sorted(xlsx_files), 1):
            filename = os.path.basename(file_path)
            try:
                # Count rows in the file
                df_count = pd.read_excel(file_path)
                count = len(df_count)
                rich_progress.print_status(f"  {i}. {filename} ({count:,} emails)", "info")
            except:
                rich_progress.print_status(f"  {i}. {filename}", "info")

if __name__ == "__main__":
    main()
